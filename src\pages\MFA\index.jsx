import { Row, Col, Image, Space, Divider } from "antd";
import Logo from "../../assets/images/logo_full.png";
import { LoadingOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { jwtDecode } from "jwt-decode";
import axios from "axios";
import { dynamoGet } from "../../service/apiDsmDynamo";
import { cognitoPutRole } from "../../service/apiCognito";

export const MFA = () => {
  const navigate = useNavigate();
  const [isProcessing, setIsProcessing] = useState(false);

  const code = window.location.href.split("code=")[1];

  const authenticate = async () => {
    if (isProcessing) return;

    setIsProcessing(true);

    try {
      console.log('🔄 Iniciando autenticação MFA...');

      if (!code) {
        console.error("❌ Código não encontrado na URL");
        return navigate("/login");
      }

      // Detectar se está em ambiente local
      const isLocalBackend = process.env.REACT_APP_LOCAL_BACKEND === 'true';

      console.log('🔍 DEBUG - Ambiente local:', isLocalBackend);
      console.log('🔍 DEBUG - Código:', code);
      let response;

      if (isLocalBackend) {
        // Em ambiente local, usar endpoint POST /cognito/token
        console.log('🏠 Ambiente LOCAL - Usando POST /cognito/token');
        response = await axios.post('/cognito/token', {
          code: code,
        });
      } else {
        // Em ambiente remoto, usar POST direto (como no original)
        console.log('☁️ Ambiente REMOTO - Usando POST para COGNITO_PARSE');
        response = await axios.post(process.env.REACT_APP_COGNITO_PARSE, {
          code: code,
        });
      }

      const {
        data: {
          data: { id_token },
        },
      } = response;

      const { email } = jwtDecode(id_token);

      localStorage.setItem("@dsm/mail", email);
      localStorage.setItem("@dsm/name", email.split("@")[0]);
      localStorage.setItem("@dsm/username", email.split("@")[0]);
      localStorage.setItem("jwt", id_token);

      const {
        data: { data },
      } = await axios.get(`${process.env.REACT_APP_API_PERMISSION}cognito/read`, {
        headers: { authorization: id_token },
      });

      let user = data?.find((user) => user.email === email);

      console.log(user);

      if (
        user[
          `${
            process.env.REACT_APP_STAGE !== "prod"
              ? process.env.REACT_APP_STAGE + "_"
              : ""
          }permission`
        ] === undefined
      ) {
        console.log("Sem permissão...");

        const users = await dynamoGet("azure-ad-users");

        const permissions = await dynamoGet(
          `${process.env.REACT_APP_STAGE}-permissions`
        );

        let role,
          u = users.find((user) => user.userPrincipalName === email);

        if (!u) {
          console.log("Usuário não encontrado...");
          return navigate("/unauthorized");
        }

        console.log("Usuário encontrado: ", u);

        switch (u?.role) {
          case "full_admin":
            role = permissions.find(
              ({ name_permission }) => name_permission === "Admin"
            ).id;
            break;

          case "basic_access":
            role = permissions.find(
              ({ name_permission }) => name_permission === "Básico"
            ).id;
            break;

          case "dcq_access":
            role = permissions.find(
              ({ name_permission }) => name_permission === "DCQ"
            ).id;
            break;

          case "financial_access":
            role = permissions.find(
              ({ name_permission }) => name_permission === "Financeiro"
            ).id;
            break;

          case "project_access":
            role = permissions.find(
              ({ name_permission }) => name_permission === "Projetos"
            ).id;
            break;

          case "sales_access":
            role = permissions.find(
              ({ name_permission }) => name_permission === "Vendas"
            ).id;
            break;

          case "security_admin":
            role = permissions.find(
              ({ name_permission }) => name_permission === "Admin Segurança"
            ).id;
            break;

          default:
            return navigate("/unauthorized");
        }

        localStorage.setItem("@dsm/permission", role);
        localStorage.setItem("@dsm/time", new Date());

        await cognitoPutRole({
          role,
          user: user.user,
          stage: process.env.REACT_APP_STAGE,
        });

        return navigate("/");
      } else {
        localStorage.setItem("@dsm/time", new Date());
        localStorage.setItem(
          "@dsm/permission",
          user[
            `${
              process.env.REACT_APP_STAGE !== "prod"
                ? process.env.REACT_APP_STAGE + "_"
                : ""
            }permission`
          ]
        );

        navigate("/");
      }
    } catch (error) {
      console.error("Erro na autenticação:", error);
      navigate("/login");
    } finally {
      setIsProcessing(false);
    }
  };

  useEffect(() => {
    if (code && !isProcessing) {
      authenticate();
    } else if (!code) {
      navigate("/login");
    }
  }, []);

  return (
    <>
      <Row
        align="middle"
        justify="center"
        style={{ minHeight: "100vh", background: "#ebedef" }}
      >
        <Col
          xs={18}
          lg={12}
          xl={6}
          style={{
            display: "flex",
            justifyContent: "center",
            padding: "2em",
            borderRadius: "10px",
            border: "1px solid #c9c9c9",
            flexDirection: "column",
            backgroundColor: "#ffffff",
          }}
        >
          <Space direction="vertical" align="center" size="middle">
            <Image preview={false} src={Logo}></Image>
            <Divider>
              Aguarde um momento...
              <br />
              Estamos te autenticando!
            </Divider>
          </Space>

          <LoadingOutlined style={{ fontSize: "48px" }} />
        </Col>
      </Row>
    </>
  );
}
