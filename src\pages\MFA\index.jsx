import { Row, Col, Image, Space, Divider } from "antd";
import Logo from "../../assets/images/logo_full.png";
import { LoadingOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import React, { useEffect } from "react";
import { jwtDecode } from "jwt-decode";

import axios from "axios";
import { dynamoGet } from "../../service/apiDsmDynamo";
import { cognitoPutRole } from "../../service/apiCognito";

const MFA = () => {
  const navigate = useNavigate();

  const code = window.location.href.split("code=")[1];

  const authenticate = async () => {
    // Criar instância específica sem withCredentials para evitar CORS
    const cognitoAxios = axios.create({
      withCredentials: false,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const {
      data: {
        data: { id_token },
      },
    } = await cognitoAxios.post(process.env.REACT_APP_COGNITO_PARSE, {
      code: code,
    });

    const { email } = jwtDecode(id_token);

    localStorage.setItem("@dsm/mail", email);
    localStorage.setItem("@dsm/name", email.split("@")[0]);
    localStorage.setItem("@dsm/username", email.split("@")[0]);
    localStorage.setItem("jwt", id_token);

    const {
      data: { data },
    } = await axios.get(`${process.env.REACT_APP_API_PERMISSION}cognito/read`, {
      headers: { authorization: id_token },
    });

    let user = data?.find((user) => user.email === email);

    console.log(user);

    if (
      user[
        `${
          process.env.REACT_APP_STAGE !== "prod"
            ? process.env.REACT_APP_STAGE + "_"
            : ""
        }permission`
      ] === undefined
    ) {
      console.log("Sem permissão...");

      const users = await dynamoGet("azure-ad-users");

      const permissions = await dynamoGet(
        `${process.env.REACT_APP_STAGE}-permissions`
      );

      let role,
        u = users.find((user) => user.userPrincipalName === email);

      if (!u) {
        console.log("Usuário não encontrado...");
        return navigate("/unauthorized");
      }

      console.log("Usuário encontrado: ", u);

      switch (u?.role) {
        case "full_admin":
          role = permissions.find(
            ({ name_permission }) => name_permission === "Admin"
          ).id;
          break;

        case "basic_access":
          role = permissions.find(
            ({ name_permission }) => name_permission === "Básico"
          ).id;
          break;

        case "dcq_access":
          role = permissions.find(
            ({ name_permission }) => name_permission === "DCQ"
          ).id;
          break;

        case "financial_access":
          role = permissions.find(
            ({ name_permission }) => name_permission === "Financeiro"
          ).id;
          break;

        case "project_access":
          role = permissions.find(
            ({ name_permission }) => name_permission === "Projetos"
          ).id;
          break;

        case "sales_access":
          role = permissions.find(
            ({ name_permission }) => name_permission === "Vendas"
          ).id;
          break;

        case "security_admin":
          role = permissions.find(
            ({ name_permission }) => name_permission === "Admin Segurança"
          ).id;
          break;

        default:
          return navigate("/unauthorized");
      }

      localStorage.setItem("@dsm/permission", role);
      localStorage.setItem("@dsm/time", new Date());

      await cognitoPutRole({
        role,
        user: user.user,
        stage: process.env.REACT_APP_STAGE,
      });

      return navigate("/");
    } else {
      localStorage.setItem("@dsm/time", new Date());
      localStorage.setItem(
        "@dsm/permission",
        user[
          `${
            process.env.REACT_APP_STAGE !== "prod"
              ? process.env.REACT_APP_STAGE + "_"
              : ""
          }permission`
        ]
      );

      navigate("/");
    }
  };

  useEffect(() => {
    async function getData() {
      await authenticate();
    }

    return getData();
  }, []);

  return (
    <>
      <Row
        align="middle"
        justify="center"
        style={{ minHeight: "100vh", background: "#ebedef" }}
      >
        <Col
          xs={18}
          lg={12}
          xl={6}
          style={{
            display: "flex",
            justifyContent: "center",
            padding: "2em",
            borderRadius: "10px",
            border: "1px solid #c9c9c9",
            flexDirection: "column",
            backgroundColor: "#ffffff",
          }}
        >
          <Space direction="vertical" align="center" size="middle">
            <Image preview={false} src={Logo}></Image>
            <Divider>
              Aguarde um momento...
              <br />
              Estamos te autenticando!
            </Divider>
          </Space>

          <LoadingOutlined style={{ fontSize: "48px" }} />
        </Col>
      </Row>
    </>
  );
};

export default MFA;
