import { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
// ✅ authService removido - não é mais necessário para MFA

const setupMfaInterceptor = () => {
  axios.interceptors.request.eject(0);

  axios.interceptors.request.use(
    (config) => {
      const originalUrl = config.url;

      const frontendPatterns = [
        'dev.dsm.darede.com.br/mfa',
        'https://dev.dsm.darede.com.br/mfa',
        'http://dev.dsm.darede.com.br/mfa',
        '/mfa?code=',
        'mfa?code='
      ];

      let shouldRedirect = false;
      for (const pattern of frontendPatterns) {
        if (originalUrl && originalUrl.includes(pattern)) {
          shouldRedirect = true;
          break;
        }
      }

      if (shouldRedirect) {
        const apiBaseURL = process.env.REACT_APP_API_PERMISSION;

        if (originalUrl.includes('?code=')) {
          const codeMatch = originalUrl.match(/code=([^&]+)/);
          if (codeMatch) {
            config.url = `${apiBaseURL}/mfa?code=${codeMatch[1]}`;
          }
        }
      }

      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
};

setupMfaInterceptor();

const detectValueType = (value) => {
  if (!value) return 'INVALID';

  if (value.startsWith('eyJ') && value.includes('.')) {
    return 'COGNITO_TOKEN';
  }

  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (uuidRegex.test(value)) {
    return 'MFA_CODE';
  }

  return 'INVALID';
};

export const useMfaCodeProcessor = () => {
  const navigate = useNavigate();
  const [mfaResult, setMfaResult] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [authToken, setAuthToken] = useState(null);
  const [processedCodes, setProcessedCodes] = useState(new Set());

  
  const processValue = useCallback(async (value) => {
    console.log('🔄 processValue chamado com:', value, 'isProcessing:', isProcessing);

    if (isProcessing) {
      console.log('⏸️ Processamento já em andamento, ignorando...');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const valueType = detectValueType(value);
      console.log('🔍 Tipo de valor detectado:', valueType);

      switch (valueType) {
        case 'COGNITO_TOKEN':
          console.log('🎫 Processando token Cognito...');
          await processAuthenticationWithToken(value);
          break;

        case 'MFA_CODE':
          console.log('🔐 Processando código MFA...');
          await processMfaCodeViaApi(value);
          break;

        default:
          console.error('❌ Valor inválido ou desconhecido:', value);
          throw new Error('Valor de autenticação inválido');
      }

      console.log('✅ Processamento concluído com sucesso');
    } catch (error) {
      console.error('❌ Erro no processamento:', error);
      setError(error);
      throw error;
    } finally {
      console.log('🏁 Finalizando processamento, setIsProcessing(false)');
      setIsProcessing(false);
    }
  }, []);

  
  const processMfaCodeViaApi = useCallback(async (code) => {
    try {
      const isLocalBackend = process.env.REACT_APP_LOCAL_BACKEND === 'true';

      let mfaResponse;

      if (isLocalBackend) {
        // Em ambiente local, usar POST /mfa
        console.log('🏠 MFA LOCAL - Usando POST /mfa');
        mfaResponse = await axios.post('/mfa', {
          code: code,
        }, {
          timeout: 30000,
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });
      } else {
        // Em ambiente remoto, usar GET /mfa?code=
        console.log('☁️ MFA REMOTO - Usando GET /mfa?code=');
        mfaResponse = await axios.get(`/mfa?code=${code}`, {
          timeout: 30000,
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });
      }

      setMfaResult(mfaResponse.data);

      if (mfaResponse.data?.token) {
        await processAuthenticationWithToken(mfaResponse.data.token);
      } else {
        // ✅ NÃO CHAMAR authenticate() - MFA já processou autenticação
        console.log('✅ Assumindo autenticação via cookies HttpOnly');
      }
    } catch (error) {
      if (error.response?.status === 404) {
        console.warn('⚠️ Endpoint /mfa não encontrado');
        console.warn('⚠️ Assumindo que autenticação foi processada via cookies HttpOnly');
        // ✅ NÃO CHAMAR authenticate() - Pode causar loops ou erros
        console.log('✅ Continuando com fluxo normal...');
      } else {
        console.error('❌ Erro no processamento MFA:', error);
        throw error;
      }
    }
  }, []);

  const processMfaCode = useCallback(async (code) => {
    if (!code || code.length < 10) {
      throw new Error('Código MFA inválido');
    }

    if (isProcessing) {
      console.warn('Processamento MFA já em andamento...');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      let retries = 0;
      const maxRetries = 10;
      while (!axios.defaults.baseURL && retries < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 200));
        retries++;
      }

      if (!axios.defaults.baseURL) {
        console.warn('⚠️ Axios ainda não configurado após aguardar, usando URL hardcoded');
      } else {
        console.log('✅ Axios configurado com sucesso:', axios.defaults.baseURL);
      }

      const isLocalBackend = process.env.REACT_APP_LOCAL_BACKEND === 'true';

      let mfaResponse;

      if (isLocalBackend) {
        // Em ambiente local, usar POST /mfa
        console.log('🏠 MFA LOCAL (processMfaCode) - Usando POST /mfa');
        mfaResponse = await axios.post('/mfa', {
          code: code,
        }, {
          timeout: 30000,
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });
      } else {
        // Em ambiente remoto, usar GET /mfa?code=
        console.log('☁️ MFA REMOTO (processMfaCode) - Usando GET /mfa?code=');
        const apiBaseURL = axios.defaults.baseURL || process.env.REACT_APP_API_PERMISSION;
        const mfaUrl = `${apiBaseURL}/mfa?code=${code}`;

        mfaResponse = await axios.get(axios.defaults.baseURL ? `/mfa?code=${code}` : mfaUrl, {
          timeout: 30000,
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });
      }

      setMfaResult(mfaResponse.data);

      if (mfaResponse.data?.success === true) {
        console.log('📊 Estado ANTES da atualização:', {
          localStorage: {
            name: localStorage.getItem('@dsm/name'),
            username: localStorage.getItem('@dsm/username'),
            mfaAuth: localStorage.getItem('mfa-authenticated')
          },
          cookies: document.cookie
        });

        try {
          const userData = mfaResponse.data.user;

          if (userData) {
            // ✅ SALVAR NO FORMATO ESPERADO PELO SISTEMA PRINCIPAL (SEM TOKEN)
            localStorage.setItem('@dsm/name', userData.name || userData.email || 'Usuário MFA');
            localStorage.setItem('@dsm/username', userData.username || userData.email || '<EMAIL>');
            localStorage.setItem('@dsm/email', userData.email || '<EMAIL>');
            localStorage.setItem('@dsm/permission', userData.permission || userData.role || 'user');
            // ✅ TOKEN NÃO É SALVO NO LOCALSTORAGE - É HTTPONLY COOKIE

            // ✅ MANTER COMPATIBILIDADE COM MFA
            localStorage.setItem('mfa-authenticated', 'true');
            localStorage.setItem('auth-user', JSON.stringify({
              id: userData.id || 'mfa-user',
              email: userData.email || '<EMAIL>',
              name: userData.name || userData.email || 'Usuário MFA',
              mfa_verified: true,
              authenticated: true,
              permission: userData.permission || userData.role || 'user'
            }));

          } else {
            localStorage.setItem('@dsm/name', 'Usuário MFA');
            localStorage.setItem('@dsm/username', '<EMAIL>');
            localStorage.setItem('@dsm/email', '<EMAIL>');
            localStorage.setItem('@dsm/permission', 'user');
            localStorage.setItem('mfa-authenticated', 'true');
          }

        } catch (error) {
          localStorage.setItem('@dsm/name', 'Usuário MFA');
          localStorage.setItem('@dsm/username', '<EMAIL>');
          localStorage.setItem('@dsm/email', '<EMAIL>');
          localStorage.setItem('@dsm/permission', 'user');
          localStorage.setItem('mfa-authenticated', 'true');

          const authEvent = new CustomEvent('authStateChanged', {
            detail: {
              authenticated: true,
              user: { id: 'mfa-user', email: '<EMAIL>' },
              method: 'mfa-fallback'
            }
          });
          window.dispatchEvent(authEvent);

          // Aguardar processamento
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        const userData = mfaResponse.data.user || { id: 'mfa-user', email: '<EMAIL>' };

        const authEvent = new CustomEvent('authStateChanged', {
          detail: {
            authenticated: true,
            user: userData,
            method: 'mfa'
          }
        });
        window.dispatchEvent(authEvent);

        const newUrl = new URL(window.location);
        newUrl.searchParams.delete('code');
        window.history.replaceState({}, '', newUrl);

        await new Promise(resolve => setTimeout(resolve, 500));

        window.location.href = '/';

        return;
      }

      if (mfaResponse.data?.token) {
        await processAuthenticationWithToken(mfaResponse.data.token);
      } else {
        // ✅ NÃO CHAMAR authenticate() - MFA já processou autenticação
        console.log('✅ Assumindo autenticação via cookies HttpOnly');
      }

    } catch (error) {
      console.error('❌ Erro no processamento MFA:', error);

      if (error.response?.status === 404) {
        console.warn('⚠️ Endpoint /mfa não encontrado');
        console.warn('⚠️ Assumindo que autenticação foi processada via cookies HttpOnly');
        // ✅ NÃO CHAMAR authenticate() - Pode causar loops ou erros
        console.log('✅ Continuando com fluxo normal...');
      } else {
        setError(error.message);
        throw error;
      }
    } finally {
      setIsProcessing(false);
    }
  }, [isProcessing]);

  /**
   * Processar autenticação com token do MFA
   */
  const processAuthenticationWithToken = useCallback(async (token) => {
    try {
      const apiBaseURL = process.env.REACT_APP_API_PERMISSION;
      const authUrl = `${apiBaseURL}/auth/set-token`;
      const authResponse = await axios.post(authUrl, {
        token,
        source: 'mfa'
      }, {
        timeout: 30000,
        withCredentials: true,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      setAuthToken(token);

      if (authResponse.data.success) {
        navigate('/');
      } else {
        throw new Error('Falha na autenticação com token');
      }

    } catch (error) {
      console.error('❌ Erro ao enviar token:', error);
      throw error;
    }
  }, [navigate]);

  // ✅ FUNÇÃO REMOVIDA: processAuthenticationWithCognito
  // Não é mais necessária - MFA processa autenticação diretamente via cookies HttpOnly

  
  useEffect(() => {
    const processValueFromUrl = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const authValue = urlParams.get('code');

      if (authValue && !isProcessing && !mfaResult && !processedCodes.has(authValue)) {
        // Em ambiente local, não precisa aguardar axios.defaults.baseURL
        const isLocalBackend = process.env.REACT_APP_LOCAL_BACKEND === 'true';

        if (!isLocalBackend && !axios.defaults.baseURL) {
          console.log('⏳ Aguardando configuração do axios...');
          return;
        }

        console.log('🔄 Processando código da URL:', authValue);

        // Marcar código como processado para evitar duplicação
        setProcessedCodes(prev => new Set([...prev, authValue]));

        const newUrl = new URL(window.location);
        newUrl.searchParams.delete('code');
        newUrl.searchParams.delete('state');
        window.history.replaceState({}, '', newUrl.pathname);

        try {
          await processValue(authValue);
        } catch (error) {
          console.error('❌ Erro no processamento automático:', error);
          // Remover código da lista de processados em caso de erro
          setProcessedCodes(prev => {
            const newSet = new Set(prev);
            newSet.delete(authValue);
            return newSet;
          });
        }
      }
    };

    // Executar apenas uma vez quando o componente monta ou quando há mudanças relevantes
    processValueFromUrl();
  }, [processValue, isProcessing, mfaResult, processedCodes]);

  const clearUrlAfterProcessing = useCallback(() => {
    const newUrl = new URL(window.location);
    newUrl.searchParams.delete('code');
    newUrl.searchParams.delete('state');
    window.history.replaceState({}, '', newUrl.pathname);
  }, []);

  return {
    mfaResult,
    isProcessing,
    error,
    authToken,
    processMfaCode,
    processValue,
    detectValueType,
    clearUrlAfterProcessing 
  };
};

export const AuthenticationHandler = () => {
  const { mfaResult, isProcessing, error } = useMfaCodeProcessor();
  const [authStatus, setAuthStatus] = useState('checking');

  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        const response = await axios.get('/auth/verify', {
          withCredentials: true 
        });

        if (response.data.authenticated) {
          setAuthStatus('authenticated');
        } else {
          setAuthStatus('unauthenticated');
        }

      } catch (error) {
        console.error('❌ Erro na verificação de autenticação:', error);
        setAuthStatus('unauthenticated');
      }
    };

    if (!isProcessing) {
      checkAuthentication();
    }
  }, [isProcessing, mfaResult]);

  useEffect(() => {
    if (mfaResult) {
      if (mfaResult.success) {
        setAuthStatus('checking');
      }
    }
  }, [mfaResult]);

  if (isProcessing) {
    return (
      <div className="auth-processing">
        <h2>🔐 Processando autenticação MFA...</h2>
        <p>Aguarde enquanto verificamos seu código de autenticação.</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="auth-error">
        <h2>❌ Erro na autenticação MFA</h2>
        <p>Erro: {error}</p>
        <button onClick={() => window.location.href = '/login'}>
          Tentar novamente
        </button>
      </div>
    );
  }

  if (authStatus === 'checking') {
    return (
      <div className="auth-checking">
        <h2>🔍 Verificando autenticação...</h2>
        <p>Aguarde...</p>
      </div>
    );
  }

  if (authStatus === 'unauthenticated') {
    return (
      <div className="auth-required">
        <h2>🔐 Autenticação necessária</h2>
        <p>Você precisa fazer login para acessar esta página.</p>
        <button onClick={() => window.location.href = '/login'}>
          Fazer Login
        </button>
      </div>
    );
  }

  return null; 
};


export const authServiceCorrected = {
  async processMfaCode(code) {
    try {
      const isLocalBackend = process.env.REACT_APP_LOCAL_BACKEND === 'true';

      let response;

      if (isLocalBackend) {
        // Em ambiente local, usar POST /mfa
        response = await axios.post('/mfa', {
          code: code,
        }, {
          withCredentials: true
        });
      } else {
        // Em ambiente remoto, usar GET /mfa?code=
        response = await axios.get(`/mfa?code=${code}`, {
          withCredentials: true
        });
      }

      return response.data;

    } catch (error) {
      console.error('❌ Erro ao processar código MFA:', error);
      throw error;
    }
  },

  /**
   * Verifica status de autenticação
   */
  async checkAuthStatus() {
    try {
      const response = await axios.get('/auth/verify', {
        withCredentials: true 
      });
      return response.data;
    } catch (error) {
      console.error('❌ Erro na verificação de autenticação:', error);
      return { authenticated: false, error: error.message };
    }
  },

  /**
   * Faz login com token Cognito
   */
  async loginWithCognitoToken(token) {
    try {
      const response = await axios.post('/auth/set-token', { token }, {
        withCredentials: true 
      });

      return response.data;

    } catch (error) {
      console.error('❌ Erro no login:', error);
      throw error;
    }
  }
};


export const dynamicApiConfigCorrected = {
  async initialize() {
    try {
      // DESABILITADO: Configuração dinâmica removida para usar apenas variáveis de ambiente
      console.log('⚠️ Configuração dinâmica desabilitada - usando REACT_APP_API_PERMISSION do .env');

      // Usar apenas a variável de ambiente
      const baseURL = process.env.REACT_APP_API_PERMISSION;

      if (baseURL) {
        axios.defaults.baseURL = baseURL;
        axios.defaults.withCredentials = true;

        console.log('✅ Axios configurado com variável de ambiente:', {
          baseURL: axios.defaults.baseURL,
          withCredentials: axios.defaults.withCredentials
        });
      } else {
        console.warn('⚠️ REACT_APP_API_PERMISSION não definida no .env');
      }

      return {
        instructions: {
          axios: {
            baseURL: baseURL,
            withCredentials: true
          }
        }
      };

    } catch (error) {
      console.error('❌ Erro na configuração:', error);
      throw error;
    }
  }
};

export default {
  useMfaCodeProcessor,
  AuthenticationHandler,
  authService: authServiceCorrected,
  dynamicApiConfig: dynamicApiConfigCorrected
};
