// IMPORTANTE: Importar supressões PRIMEIRO (antes do React)
import "./utils/reactStrictModeSuppress";
import "./utils/suppressFindDOMNode";
import "./utils/antdWarningSuppress";
import "./utils/suppressPerformanceViolations";

import React from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import "./styles/theme.css";
import "./styles/antd-custom.css";
import "./styles/layout-fixes.css";
import App from "./App";
import reportWebVitals from "./reportWebVitals";
import { store, persistor } from "./store/store";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { initializeStorageCleanup } from "./utils/clearCorruptedStorage";
import { initializePerformanceOptimizations } from "./utils/performanceOptimization";
import { initializeErrorSuppression } from "./utils/errorSuppression";

// Initialize error suppression FIRST to catch all warnings
initializeErrorSuppression();

// DEBUG: Log da variável de ambiente
console.log('🔍 DEBUG - REACT_APP_API_PERMISSION:', process.env.REACT_APP_API_PERMISSION);

// Configurar axios global imediatamente
import axios from 'axios';
if (process.env.REACT_APP_API_PERMISSION) {
  axios.defaults.baseURL = process.env.REACT_APP_API_PERMISSION;
  axios.defaults.withCredentials = true;
  console.log('✅ Axios configurado no index.js:', {
    baseURL: axios.defaults.baseURL,
    withCredentials: axios.defaults.withCredentials
  });
} else {
  console.error('❌ REACT_APP_API_PERMISSION não definida no .env');
}

// Interceptor global para debug de requisições (apenas em desenvolvimento)
if (process.env.NODE_ENV === 'development') {
  axios.interceptors.request.use(
    (config) => {
      const fullUrl = config.baseURL ? `${config.baseURL}${config.url}` : config.url;
      console.log(`🌐 REQUEST: ${config.method?.toUpperCase()} ${fullUrl}`);
      return config;
    },
    (error) => {
      console.error('❌ Request Error:', error);
      return Promise.reject(error);
    }
  );
}

// Global error handler for debugging
window.addEventListener('error', (event) => {
  console.error('Global Error:', event.error);
  console.error('Message:', event.message);
  console.error('Filename:', event.filename);
  console.error('Line:', event.lineno);
  console.error('Column:', event.colno);
  console.error('Stack:', event.error?.stack);

  // Store error in sessionStorage to persist across reloads
  const errorLog = {
    timestamp: new Date().toISOString(),
    message: event.message,
    filename: event.filename,
    line: event.lineno,
    column: event.colno,
    stack: event.error?.stack
  };

  const existingErrors = JSON.parse(sessionStorage.getItem('errorLog') || '[]');
  existingErrors.push(errorLog);
  sessionStorage.setItem('errorLog', JSON.stringify(existingErrors.slice(-10))); // Keep last 10 errors
});

// Global unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled Promise Rejection:', event.reason);
  console.error('Promise:', event.promise);
  if (event.reason?.stack) {
    console.error('Stack:', event.reason.stack);
  }

  // Store promise rejection in sessionStorage
  const errorLog = {
    timestamp: new Date().toISOString(),
    type: 'unhandledrejection',
    reason: event.reason?.toString(),
    stack: event.reason?.stack
  };

  const existingErrors = JSON.parse(sessionStorage.getItem('errorLog') || '[]');
  existingErrors.push(errorLog);
  sessionStorage.setItem('errorLog', JSON.stringify(existingErrors.slice(-10)));
});

// Initialize storage cleanup before app starts
initializeStorageCleanup();

// Initialize performance optimizations
initializePerformanceOptimizations();

// Add global debug functions (only in development)
if (process.env.NODE_ENV === 'development') {
  window.showErrors = () => {
    const errors = JSON.parse(sessionStorage.getItem('errorLog') || '[]');
    return errors;
  };

  window.clearErrors = () => {
    sessionStorage.removeItem('errorLog');
  };
}

const container = document.getElementById("root");
const root = createRoot(container);

// Componente condicional para StrictMode
const AppWrapper = ({ children }) => {
  // Desabilitar StrictMode temporariamente para evitar warnings do findDOMNode do Antd
  // TODO: Reabilitar quando Antd for totalmente compatível com React 18
  const useStrictMode = process.env.REACT_APP_ENABLE_STRICT_MODE === 'true';

  if (useStrictMode) {
    return <React.StrictMode>{children}</React.StrictMode>;
  }

  return children;
};

root.render(
  <AppWrapper>
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <App />
      </PersistGate>
    </Provider>
  </AppWrapper>
);

reportWebVitals();
