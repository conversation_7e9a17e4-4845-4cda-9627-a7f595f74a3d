/**
 * Serviço de Configuração Dinâmica da API
 * Busca a configuração real da API e configura URLs automaticamente
 */

import axios from 'axios';

class DynamicApiConfigService {
  constructor() {
    this.config = null;
    this.isConfigured = false;
    this.configPromise = null;
  }

  /**
   * Busca a configuração da API dinamicamente
   */
  async fetchApiConfig() {
    if (this.configPromise) {
      return this.configPromise;
    }

    this.configPromise = this._doFetchConfig();
    return this.configPromise;
  }

  async _doFetchConfig() {
    console.log('⚠️ DESABILITADO: Configuração dinâmica removida - usando apenas REACT_APP_API_PERMISSION');

    this.config = {
      axios: {
        baseURL: process.env.REACT_APP_API_PERMISSION,
        withCredentials: true
      },
      endpoints: {
        auth: '/auth',
        cognito: '/cognito/read',
        mfaSecrets: '/mfa/secrets'
      }
    };

    console.log('✅ Configuração da API definida com variável de ambiente:', this.config);
    await this.configureAxios();

    return this.config;
  }

  /**
   * Configura o axios com as URLs corretas
   */
  async configureAxios() {
    if (!this.config) {
      throw new Error('Configuração não disponível');
    }

    const { axios: axiosConfig } = this.config;
    
    console.log('⚙️ Configurando axios com:', axiosConfig);
    
    // Não modificar axios global para evitar conflitos
    // Apenas armazenar a configuração para uso posterior
    this.axiosConfig = {
      baseURL: axiosConfig.baseURL,
      withCredentials: axiosConfig.withCredentials || true,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    this.isConfigured = true;
    console.log('✅ Axios configurado com sucesso');
  }

  /**
   * Obtém a URL completa para um endpoint
   */
  getEndpointUrl(endpoint) {
    if (!this.config) {
      console.warn('⚠️ Configuração não disponível, usando URL padrão');
      const fallbackUrl = process.env.REACT_APP_API_PERMISSION;
      return `${fallbackUrl}${endpoint}`;
    }

    const baseURL = this.config.axios.baseURL;
    const endpointPath = this.config.endpoints?.[endpoint] || endpoint;
    
    return `${baseURL}${endpointPath}`;
  }

  /**
   * Verifica se a configuração está pronta
   */
  isReady() {
    return this.isConfigured && this.config !== null;
  }

  /**
   * Obtém a configuração atual
   */
  getConfig() {
    return this.config;
  }

  /**
   * Força uma nova busca da configuração
   */
  async refreshConfig() {
    this.config = null;
    this.isConfigured = false;
    this.configPromise = null;
    
    return this.fetchApiConfig();
  }

  /**
   * Cria uma instância do axios configurada
   */
  createConfiguredAxios() {
    if (!this.axiosConfig) {
      throw new Error('Configuração não disponível. Chame fetchApiConfig() primeiro.');
    }

    const instance = axios.create(this.axiosConfig);

    // Interceptors para a instância (com validação)
    instance.interceptors.request.use(
      (config) => {
        const url = config.url || '';
        const method = config.method?.toUpperCase() || 'GET';
        console.log(`📡 Instance Request: ${method} ${url}`);
        return config;
      },
      (error) => {
        console.error('❌ Instance Request Error:', error);
        return Promise.reject(error);
      }
    );

    instance.interceptors.response.use(
      (response) => {
        const url = response.config?.url || '';
        console.log(`✅ Instance Response: ${response.status} ${url}`);
        return response;
      },
      (error) => {
        const url = error.config?.url || '';
        const status = error.response?.status || 'unknown';
        console.error(`❌ Instance Error: ${status} ${url}`, error.message);
        return Promise.reject(error);
      }
    );

    return instance;
  }
}

// Instância singleton
export const dynamicApiConfig = new DynamicApiConfigService();

export async function initializeApiConfig() {
  try {
    // DESABILITADO: Configuração dinâmica removida para usar apenas variáveis de ambiente
    console.log('⚠️ Configuração dinâmica desabilitada - usando apenas REACT_APP_API_PERMISSION do .env');

    // Configurar axios global com variável de ambiente
    const baseURL = process.env.REACT_APP_API_PERMISSION;

    if (baseURL) {
      const axios = (await import('axios')).default;
      axios.defaults.baseURL = baseURL;
      axios.defaults.withCredentials = true;

      console.log('✅ Axios configurado com variável de ambiente:', {
        baseURL: axios.defaults.baseURL,
        withCredentials: axios.defaults.withCredentials
      });
    } else {
      console.warn('⚠️ REACT_APP_API_PERMISSION não definida no .env');
    }

    return true;
  } catch (error) {
    console.error('❌ Falha na configuração:', error);
    return false;
  }
}

// Função utilitária para fazer requisições com configuração dinâmica
export async function makeConfiguredRequest(endpoint, options = {}) {
  if (!dynamicApiConfig.isReady()) {
    await dynamicApiConfig.fetchApiConfig();
  }

  const axiosInstance = dynamicApiConfig.createConfiguredAxios();
  
  return axiosInstance({
    url: endpoint,
    ...options
  });
}

export default dynamicApiConfig;
